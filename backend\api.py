"""
Simplified API for JoMaDe application.
This replaces the complex API structure with a simpler, flatter approach.
"""

from fastapi import FastAP<PERSON>, HTTPException, Body, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional
import logging
import os
import json
import asyncio
import threading
from queue import Queue
from datetime import datetime

# Import our simplified storage
from storage import JobUrlStore, CVStore, JobStore, ShortlistStore
from scraper import create_scraper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global queue for real-time logging
log_queue = Queue()
scraping_active = False

def add_log_message(message_type: str, message: str, data: dict = None):
    """Add a message to the log queue for real-time streaming"""
    log_data = {
        "type": message_type,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }
    if data:
        log_data.update(data)

    try:
        log_queue.put_nowait(json.dumps(log_data))
    except Exception as e:
        logger.error(f"Failed to add log message: {e}")

# Create the FastAPI app
app = FastAPI(
    title="JoMaDe API",
    description="Job Market Detector API - Simplified Version",
    version="0.2.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize data stores
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
os.makedirs(DATA_DIR, exist_ok=True)

job_urls_store = JobUrlStore(os.path.join(DATA_DIR, "job_urls.json"))
cv_store = CVStore(os.path.join(DATA_DIR, "cv.json"))
jobs_store = JobStore(os.path.join(DATA_DIR, "jobs.json"))
shortlist_store = ShortlistStore(os.path.join(DATA_DIR, "shortlist.json"))
# Ensure shortlist store loads existing data
shortlist_store.load()

# Migrate data from existing markdown files if available
def migrate_from_markdown():
    """Migrate data from markdown files to JSON if needed."""
    try:
        # Check for job_url.md
        job_url_md_path = os.path.join(os.path.dirname(__file__), "job_url.md")
        if os.path.exists(job_url_md_path) and len(job_urls_store.get_all()) == 0:
            logger.info(f"Migrating data from {job_url_md_path}")
            with open(job_url_md_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Skip comment lines (first 10 lines)
            for i, line in enumerate(lines[10:], start=0):
                url = line.strip()
                if url and not url.startswith('#'):
                    job_urls_store.add({"url": url})

            logger.info(f"Migrated {len(job_urls_store.get_all())} URLs from markdown")

        # Check for CV_Summary.md
        cv_md_path = os.path.join(os.path.dirname(__file__), "CV_Summary.md")
        if os.path.exists(cv_md_path) and len(cv_store.get_all()) == 0:
            logger.info(f"Migrating data from {cv_md_path}")
            with open(cv_md_path, 'r', encoding='utf-8') as f:
                content = f.read()

            cv_store.add({"summary": content})
            logger.info("Migrated CV summary from markdown")

    except Exception as e:
        logger.error(f"Error migrating data: {str(e)}")

# Try to migrate data on startup
migrate_from_markdown()

# API Routes

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to JoMaDe API - Simplified Version",
        "version": "0.2.0",
        "docs_url": "/docs"
    }

# Job URLs endpoints
@app.get("/api/urls")
async def get_urls():
    """Get all job URLs"""
    return job_urls_store.get_all()

@app.post("/api/urls")
async def add_url(url_data: Dict[str, Any] = Body(...)):
    """Add a new job URL"""
    if "url" not in url_data:
        raise HTTPException(status_code=400, detail="URL is required")

    return job_urls_store.add(url_data)

@app.delete("/api/urls/{url_id}")
async def delete_url(url_id: str):
    """Delete a job URL"""
    if job_urls_store.delete(url_id):
        return {"message": f"URL with ID {url_id} deleted successfully"}
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

@app.put("/api/urls/{url_id}")
async def update_url(url_id: str, url_data: Dict[str, Any] = Body(...)):
    """Update a job URL"""
    result = job_urls_store.update(url_id, url_data)
    if result:
        return result
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

# CV endpoints
@app.get("/api/cv")
async def get_cv():
    """Get CV data"""
    cv_data = cv_store.get_all()
    if cv_data:
        return cv_data[0]
    return {"summary": ""}

@app.put("/api/cv")
async def update_cv(cv_data: Dict[str, Any] = Body(...)):
    """Update CV data"""
    if "summary" not in cv_data:
        raise HTTPException(status_code=400, detail="Summary is required")

    cv_entries = cv_store.get_all()
    if cv_entries:
        return cv_store.update(cv_entries[0]["id"], cv_data)
    return cv_store.add(cv_data)

# Jobs endpoints
@app.get("/api/jobs")
async def get_jobs(source: Optional[str] = Query(None), shortlisted: Optional[bool] = Query(None)):
    """
    Get jobs with optional filtering

    Args:
        source: Filter by source prefix (e.g., AAA)
        shortlisted: Filter by shortlisted status
    """
    if source:
        return jobs_store.get_by_source(source)
    if shortlisted is not None:
        if shortlisted:
            return jobs_store.get_shortlisted()
        return [job for job in jobs_store.get_all() if not job.get('isShortlisted', False)]
    return jobs_store.get_all()

@app.post("/api/jobs")
async def add_job(job_data: Dict[str, Any] = Body(...)):
    """Add a new job"""
    return jobs_store.add(job_data)

@app.put("/api/jobs/{job_id}")
async def update_job(job_id: str, job_data: Dict[str, Any] = Body(...)):
    """Update a job"""
    result = jobs_store.update(job_id, job_data)
    if result:
        return result
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

@app.delete("/api/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete a job"""
    if jobs_store.delete(job_id):
        return {"message": f"Job with ID {job_id} deleted successfully"}
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

# Scraping statistics endpoint
@app.get("/api/scrape/stats")
async def get_scraping_stats():
    """Get statistics about scraped jobs and caching status."""
    stats = jobs_store.get_scraping_stats()
    return {
        "success": True,
        "stats": stats,
        "timestamp": datetime.now().isoformat()
    }

# Source statistics endpoint
@app.get("/api/sources/stats")
async def get_source_statistics():
    """Get statistics for each source URL."""
    try:
        source_stats = jobs_store.get_source_statistics()
        return {"success": True, "source_stats": source_stats}
    except Exception as e:
        logger.error(f"Error getting source statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get source statistics: {str(e)}")

# Job shortlisting endpoint
@app.post("/api/jobs/shortlist")
async def shortlist_jobs(min_score: float = Query(0.7, description="Minimum match score (temperature level)"),
                        max_jobs: int = Query(20, description="Maximum number of jobs to shortlist"),
                        min_confidence: float = Query(75.0, description="Minimum confidence percentage to display")):
    """Shortlist jobs based on CV match with enhanced deduplication and persistence."""
    try:
        # Get CV data
        cv_data = cv_store.get_all()
        if not cv_data:
            raise HTTPException(status_code=400, detail="No CV data found. Please add CV information first.")

        # Use the first CV entry (assuming single user for now)
        cv_info = cv_data[0] if isinstance(cv_data, list) else cv_data

        # Shortlist jobs with new parameters
        shortlisted_jobs = jobs_store.shortlist_jobs_by_cv(cv_info, min_score, max_jobs, min_confidence)

        # Enhance shortlisted jobs with full URL information
        enhanced_jobs = []
        for job in shortlisted_jobs:
            enhanced_job = job.copy()

            # Get full URL for the source prefix
            source_prefix = job.get('source', '')
            if source_prefix:
                # Find the full URL for this prefix
                for url_data in job_urls_store.get_all():
                    if url_data.get('prefix') == source_prefix:
                        enhanced_job['source_url'] = url_data.get('url', '')
                        enhanced_job['source_display'] = f"{source_prefix}: {url_data.get('url', '')}"
                        break
                else:
                    enhanced_job['source_url'] = ''
                    enhanced_job['source_display'] = source_prefix
            else:
                enhanced_job['source_url'] = ''
                enhanced_job['source_display'] = 'Unknown'

            enhanced_jobs.append(enhanced_job)

        return {
            "success": True,
            "message": f"Found {len(enhanced_jobs)} matching jobs",
            "shortlisted_jobs": enhanced_jobs,
            "total_jobs": len(jobs_store.get_all()),
            "min_score": min_score,
            "min_confidence": min_confidence,
            "parameters": {
                "temperature_level": min_score,
                "confidence_threshold": min_confidence,
                "max_results": max_jobs
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error shortlisting jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to shortlist jobs: {str(e)}")

# Shortlist management endpoints
@app.get("/api/shortlist/stats")
async def get_shortlist_statistics():
    """Get statistics about the shortlist."""
    try:
        stats = shortlist_store.get_shortlist_statistics()
        return {
            "success": True,
            "statistics": stats
        }
    except Exception as e:
        logger.error(f"Error getting shortlist statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get shortlist statistics: {str(e)}")

@app.get("/api/shortlist")
async def get_shortlist(min_confidence: float = Query(75.0, description="Minimum confidence percentage")):
    """Get the current shortlist with minimum confidence filter."""
    try:
        shortlist = shortlist_store.get_active_shortlist(min_confidence)
        return {
            "success": True,
            "shortlist": shortlist,
            "count": len(shortlist),
            "min_confidence": min_confidence
        }
    except Exception as e:
        logger.error(f"Error getting shortlist: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get shortlist: {str(e)}")

@app.delete("/api/shortlist/{job_id}")
async def remove_from_shortlist(job_id: str):
    """Remove a job from the shortlist."""
    try:
        removed = shortlist_store.remove_job_from_shortlist(job_id)
        if removed:
            return {"success": True, "message": f"Job {job_id} removed from shortlist"}
        else:
            raise HTTPException(status_code=404, detail="Job not found in shortlist")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing job from shortlist: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to remove job from shortlist: {str(e)}")

@app.get("/api/shortlist/cv-status")
async def get_cv_change_status():
    """Get CV change status and shortlist refresh information."""
    try:
        # Get current CV summary
        cv_info = cv_store.get_cv_summary()
        if not cv_info:
            return {
                "success": False,
                "message": "No CV summary found. Please save your CV first.",
                "status": {
                    'has_shortlist': False,
                    'cv_changed': False,
                    'needs_refresh': False
                }
            }

        cv_summary = cv_info.get('summary', '')
        status = shortlist_store.get_cv_change_status(cv_summary)

        return {
            "success": True,
            "status": status
        }
    except Exception as e:
        logger.error(f"Error getting CV change status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get CV change status: {str(e)}")

@app.post("/api/shortlist/refresh")
async def refresh_shortlist():
    """Refresh shortlist when CV has changed."""
    try:
        # Get current CV summary
        cv_info = cv_store.get_cv_summary()
        if not cv_info:
            raise HTTPException(status_code=400, detail="No CV summary found. Please save your CV first.")

        cv_summary = cv_info.get('summary', '')
        refreshed = shortlist_store.refresh_shortlist_for_cv_change(cv_summary)

        if refreshed:
            return {
                "success": True,
                "message": "Shortlist marked for refresh due to CV changes. Please run shortlisting again to get updated matches.",
                "refreshed": True
            }
        else:
            return {
                "success": True,
                "message": "No refresh needed - CV has not changed since last shortlisting.",
                "refreshed": False
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing shortlist: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh shortlist: {str(e)}")

# Scraping endpoint
@app.post("/api/scrape")
async def scrape_jobs(force_scrape: bool = Query(False, description="Force scraping even if done today")):
    """
    Trigger job scraping using Firecrawl API with date-based caching and duplicate detection.

    Args:
        force_scrape: If True, scrape even if already done today
    """
    global scraping_active

    try:
        # Set scraping as active
        scraping_active = True

        # Clear any old messages from the queue
        while not log_queue.empty():
            try:
                log_queue.get_nowait()
            except:
                break

        add_log_message("info", "🚀 Starting scraping process...")

        # Check if scraping was already done today (unless forced)
        if not force_scrape and jobs_store.was_scraped_today():
            last_scrape_date = jobs_store.get_last_scrape_date()
            stats = jobs_store.get_scraping_stats()

            add_log_message("info", f"📅 Scraping already performed today ({last_scrape_date})")
            add_log_message("info", f"📊 Current stats: {stats['scraped_jobs']} scraped jobs, {stats['total_jobs']} total jobs")
            add_log_message("warning", "⏭️  Skipping scraping to avoid duplicates. Use force_scrape=true to override.")
            add_log_message("complete", "🎯 Using existing data from today's scraping")

            scraping_active = False
            return {
                "success": True,
                "message": f"Using existing data from today's scraping ({last_scrape_date})",
                "job_count": stats['scraped_jobs'],
                "total_jobs": stats['total_jobs'],
                "was_cached": True,
                "last_scrape_date": last_scrape_date,
                "stats": stats,
                "timestamp": datetime.now().isoformat()
            }

        # Get URLs from storage
        urls = job_urls_store.get_all()

        add_log_message("info", f"📋 Found {len(urls)} URLs to scrape")
        if force_scrape:
            add_log_message("warning", "🔄 Force scraping enabled - will scrape even if done today")

        if not urls:
            add_log_message("error", "❌ No URLs configured for scraping")
            scraping_active = False
            return {
                "success": False,
                "message": "No URLs configured for scraping",
                "url_count": 0,
                "job_count": 0
            }

        # Create scraper instance with timeout (3 minutes per URL)
        add_log_message("info", "🔧 Initializing scraper with 3-minute timeout per URL...")
        scraper = create_scraper(timeout_seconds=180)  # 3 minutes per URL
        if not scraper:
            # Fail hard if scraper can't be created (missing API keys)
            error_msg = "Scraper not available - missing API keys or configuration"
            add_log_message("error", f"❌ {error_msg}")
            logger.error(error_msg)
            scraping_active = False
            return {
                "success": False,
                "message": error_msg,
                "url_count": len(urls),
                "job_count": 0,
                "failed_urls": [url.get("url", "") for url in urls],
                "successful_urls": []
            }

        # Prepare URLs with prefixes for scraping
        urls_with_prefixes = [
            {"url": url.get("url", ""), "prefix": url.get("prefix", "AAA")}
            for url in urls
            if url.get("url")
        ]

        add_log_message("info", f"📋 Prepared {len(urls_with_prefixes)} URLs for scraping")

        # Perform actual scraping in a separate thread to allow real-time logging
        def run_scraping():
            return scraper.scrape_multiple_urls(urls_with_prefixes, log_callback=add_log_message)

        # Run scraping in thread to not block the async endpoint
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_scraping)
            scrape_result = future.result()  # This will wait for completion

        # Store scraped jobs with duplicate detection
        add_log_message("info", f"💾 Storing {len(scrape_result.get('jobs', []))} jobs with duplicate detection...")

        added_jobs = 0
        duplicate_jobs = 0

        for job in scrape_result.get("jobs", []):
            result_job = jobs_store.add_if_not_duplicate(job)
            if result_job and result_job.get('id') == job.get('id'):
                # New job was added
                added_jobs += 1
            else:
                # Duplicate was found
                duplicate_jobs += 1

        add_log_message("success", f"✅ Scraping completed!")
        add_log_message("info", f"📊 Results: {added_jobs} new jobs added, {duplicate_jobs} duplicates skipped")
        add_log_message("complete", "🎯 All operations finished successfully")

        # Get final stats
        final_stats = jobs_store.get_scraping_stats()

        # Mark scraping as complete
        scraping_active = False

        return {
            "success": scrape_result["success"],
            "timestamp": scrape_result["timestamp"],
            "url_count": scrape_result["url_count"],
            "successful_urls": scrape_result["successful_urls"],
            "failed_urls": scrape_result["failed_urls"],
            "job_count": scrape_result["job_count"],
            "new_jobs_added": added_jobs,
            "duplicates_skipped": duplicate_jobs,
            "total_jobs": final_stats['total_jobs'],
            "was_cached": False,
            "stats": final_stats,
            "message": f"Scraping completed: {added_jobs} new jobs added, {duplicate_jobs} duplicates skipped"
        }

    except Exception as e:
        logger.error(f"Error in scrape_jobs: {str(e)}")
        add_log_message("error", f"❌ Scraping failed: {str(e)}")
        scraping_active = False
        return {
            "success": False,
            "message": f"Scraping failed: {str(e)}",
            "url_count": len(job_urls_store.get_all()),
            "job_count": 0
        }


# Mock job generation removed - system now fails hard on scraping errors

@app.post("/clean-mock-jobs")
async def clean_mock_jobs():
    """Remove all mock jobs from the database."""
    try:
        removed_count = jobs_store.remove_mock_jobs()
        stats = jobs_store.get_scraping_stats()

        return {
            "success": True,
            "message": f"Removed {removed_count} mock jobs",
            "removed_count": removed_count,
            "remaining_jobs": stats['scraped_jobs'],
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Error cleaning mock jobs: {str(e)}")
        return {
            "success": False,
            "message": f"Failed to clean mock jobs: {str(e)}"
        }

# Simple frontend endpoints (matching the HTML frontend expectations)
@app.get("/job-urls")
async def get_job_urls_simple():
    """Get job URLs with prefix information for HTML frontend"""
    urls = job_urls_store.get_all()
    return {
        "urls": [url.get("url", "") for url in urls],
        "url_data": urls  # Include full URL data with prefixes
    }

@app.post("/job-urls")
async def save_job_urls_simple(data: Dict[str, List[str]] = Body(...)):
    """Save job URLs from HTML frontend with duplicate prevention"""
    urls = data.get("urls", [])

    if not urls:
        return {"message": "No URLs provided"}

    added_count = 0
    duplicate_count = 0

    # Add URLs one by one with duplicate checking
    for url in urls:
        url = url.strip()
        if url:
            try:
                existing = job_urls_store.find_by_url(url)
                if existing:
                    duplicate_count += 1
                    logger.info(f"Skipping duplicate URL: {url} (existing prefix: {existing.get('prefix')})")
                else:
                    job_urls_store.add({"url": url})
                    added_count += 1
                    logger.info(f"Added new URL: {url}")
            except Exception as e:
                logger.error(f"Error adding URL {url}: {str(e)}")

    return {
        "message": f"Processed {len(urls)} URLs: {added_count} added, {duplicate_count} duplicates skipped",
        "added": added_count,
        "duplicates": duplicate_count,
        "total_urls": len(job_urls_store.get_all())
    }

@app.post("/api/urls/deduplicate")
async def deduplicate_urls():
    """Remove duplicate URLs from the store"""
    try:
        removed_count = job_urls_store.deduplicate()
        return {
            "success": True,
            "message": f"Deduplication complete: removed {removed_count} duplicates",
            "removed": removed_count,
            "remaining": len(job_urls_store.get_all())
        }
    except Exception as e:
        logger.error(f"Error during deduplication: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Deduplication failed: {str(e)}")

@app.post("/api/urls/normalize")
async def normalize_url_prefixes():
    """Normalize URL prefixes to sequential order (AAA, AAB, AAC, etc.)"""
    try:
        updated_count = job_urls_store.normalize_prefixes()
        return {
            "success": True,
            "message": f"Prefix normalization complete: updated {updated_count} prefixes",
            "updated": updated_count,
            "total": len(job_urls_store.get_all())
        }
    except Exception as e:
        logger.error(f"Error during prefix normalization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prefix normalization failed: {str(e)}")

@app.post("/api/urls/cleanup")
async def cleanup_urls():
    """Complete URL cleanup: deduplicate and normalize prefixes"""
    try:
        # First deduplicate
        removed_count = job_urls_store.deduplicate()
        # Then normalize prefixes
        updated_count = job_urls_store.normalize_prefixes()

        return {
            "success": True,
            "message": f"URL cleanup complete: removed {removed_count} duplicates, updated {updated_count} prefixes",
            "removed_duplicates": removed_count,
            "updated_prefixes": updated_count,
            "final_count": len(job_urls_store.get_all())
        }
    except Exception as e:
        logger.error(f"Error during URL cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"URL cleanup failed: {str(e)}")

@app.get("/cv-summary")
async def get_cv_summary_simple():
    """Get CV summary for HTML frontend"""
    cv_data = cv_store.get_all()
    if cv_data:
        return {"summary": cv_data[0].get("summary", "")}
    return {"summary": ""}

@app.post("/cv-summary")
async def save_cv_summary_simple(data: Dict[str, str] = Body(...)):
    """Save CV summary from HTML frontend"""
    summary = data.get("summary", "")

    cv_entries = cv_store.get_all()
    if cv_entries:
        cv_store.update(cv_entries[0]["id"], {"summary": summary})
    else:
        cv_store.add({"summary": summary})

    return {"message": "CV summary saved successfully"}

@app.post("/scrape-jobs")
async def scrape_jobs_simple():
    """Scrape jobs endpoint for HTML frontend"""
    return await scrape_jobs()

@app.get("/jobs")
async def get_jobs_simple():
    """Get jobs in simple format for HTML frontend"""
    jobs = jobs_store.get_all()
    return {"jobs": jobs}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint with real-time job counts"""
    # Get fresh statistics to ensure accurate counts
    stats = jobs_store.get_scraping_stats()
    return {
        "status": "healthy",
        "api_version": "0.2.0",
        "storage": {
            "job_urls": len(job_urls_store.get_all()),
            "cv": len(cv_store.get_all()),
            "jobs": stats['scraped_jobs']  # Only show scraped jobs since database is cleaned
        }
    }

# Server-Sent Events endpoint for real-time logging
@app.get("/api/scrape/logs")
async def scrape_logs():
    """Stream real-time scraping logs to the frontend"""

    async def event_stream():
        while True:
            try:
                # Check if there are messages in the queue
                if not log_queue.empty():
                    message = log_queue.get_nowait()
                    yield f"data: {message}\n\n"
                else:
                    # Send heartbeat to keep connection alive
                    yield f"data: {{'type': 'heartbeat', 'timestamp': '{datetime.now().isoformat()}'}}\n\n"

                await asyncio.sleep(0.1)  # Small delay to prevent overwhelming

                # Stop streaming if scraping is not active and queue is empty
                if not scraping_active and log_queue.empty():
                    yield f"data: {{'type': 'complete', 'message': 'Scraping completed'}}\n\n"
                    break

            except Exception as e:
                logger.error(f"Error in event stream: {e}")
                yield f"data: {{'type': 'error', 'message': 'Stream error: {str(e)}'}}\n\n"
                break

    return StreamingResponse(
        event_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

# Test endpoint to debug URL loading
@app.get("/test/urls")
async def test_urls():
    """Test endpoint to check URL loading"""
    urls = job_urls_store.get_all()
    return {
        "count": len(urls),
        "urls": urls[:3],  # Show first 3 URLs for testing
        "message": f"Found {len(urls)} URLs in storage"
    }

# Run the application with: uvicorn api:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)

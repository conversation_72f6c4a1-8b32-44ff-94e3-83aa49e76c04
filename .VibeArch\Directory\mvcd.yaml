codebase:
- file: backend/api.py
  element: migrate_from_markdown
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - scraper
  - storage
  - uvicorn
  loc: 235
  last_modified: 1748277941.2203536
- file: backend/scraper.py
  element: JobScraper
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - dotenv
  - firecrawl
  - logging
  - openai
  loc: 165
  last_modified: 1748282215.0030499
- file: backend/storage.py
  element: JsonStore
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  loc: 95
  last_modified: 1747376281.9076874
- file: test_single_crawl.py
  element: test_single_url
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - scraper
  loc: 32
  last_modified: 1748282276.5997152
